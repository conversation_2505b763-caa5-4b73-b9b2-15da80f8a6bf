variable "project_name" {
  type        = string
  description = "Name for project. Will be used as prefix for created entities"
}

variable "region" {
  type        = string
  description = "Deployment region. Note that Lambda@Edge will always deploy to us-east-1 and spread to every region automatically"
}

variable "certificate_arn" {
  type        = string
  description = "Arn of certificate that will be used for CloudFront distribution"
}

variable "domain" {
  type        = string
  description = "Domain that application will run"
}

variable "cloudflare_zone_id" {
  type = string
}

variable "cloudflare_token" {
  type = string
}
