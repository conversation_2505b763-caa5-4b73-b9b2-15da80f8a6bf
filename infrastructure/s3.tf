// Bucket that will contain resources from /assets
resource "aws_s3_bucket" "app_bucket" {
  bucket        = "${local.resource_prefix}-assets"
  acl           = "private"
  force_destroy = true
  website {
    index_document = "index.html"
    error_document = "index.html"
  }
}

locals {
  content_type_map = {
    html = "text/html",
    js   = "application/javascript",
    css  = "text/css",
    svg  = "image/svg+xml",
    jpg  = "image/jpeg",
    ico  = "image/x-icon",
    png  = "image/png",
    gif  = "image/gif",
    pdf  = "application/pdf"
  }
}

// Push all /build into bucket
resource "aws_s3_bucket_object" "assets" {
  for_each = fileset("${local.build_dir}", "**")
  bucket   = aws_s3_bucket.app_bucket.id
  key      = each.value
  source   = "${local.build_dir}/${each.value}"
  etag     = filemd5("${local.build_dir}/${each.value}")

  content_type = lookup(local.content_type_map, regex("\\.?(?P<extension>[A-Za-z0-9]+)$", each.value).extension, "application/octet-stream")
}
