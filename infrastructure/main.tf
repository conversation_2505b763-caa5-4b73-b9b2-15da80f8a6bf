terraform {
  required_version = ">= 0.12.0"

  backend "s3" {
    bucket         = "esto-partner-v2-terraform-state"
    key            = "terraform.tfstate"
    region         = "eu-central-1"
    dynamodb_table = "esto-partner-v2-terraform-lock"
  }

  required_providers {
    cloudflare = {
      source = "cloudflare/cloudflare"
    }
    external = {
      source  = "hashicorp/external"
      version = "2.0.0"
    }
    null = {
      source = "hashicorp/null"
    }
    archive = {
      source = "hashicorp/archive"
    }
  }
}

provider "aws" {
  region = var.region
}

provider "aws" {
  region = "us-east-1"
  alias  = "aws-us-east-1"
}

provider "cloudflare" {
  api_token = var.cloudflare_token
}
locals {
  project_dir     = "${path.module}/.."
  build_dir       = "${local.project_dir}/build"
  resource_prefix = "${var.project_name}-${terraform.workspace}"
}
