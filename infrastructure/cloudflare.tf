data "http" "cloudflare_dns_record" {
  url = "https://api.cloudflare.com/client/v4/zones/${var.cloudflare_zone_id}/dns_records?type=CNAME&name=${var.domain}&page=1&per_page=10&match=all"

  request_headers = {
    Authorization = "Bearer ${var.cloudflare_token}"
    Content-Type  = "application/jsoncurl"
  }
}

locals {
  cloudflare_dns_record_exists = try(element(jsondecode(data.http.cloudflare_dns_record.body).result, 0).name, "") == var.domain ? true : false
}

output "cloudfront_alias_set" {
  value = local.cloudflare_dns_record_exists
}

resource "cloudflare_record" "domain" {
  zone_id = var.cloudflare_zone_id
  name    = var.domain
  value   = aws_cloudfront_distribution.app_distribution.domain_name
  type    = "CNAME"
  ttl     = 1
}
