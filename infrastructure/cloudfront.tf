resource "aws_cloudfront_origin_access_identity" "origin_access_identity" {
  comment = "CloudFront origin access identity"
}

data "aws_iam_policy_document" "s3_policy" {
  statement {
    actions   = ["s3:GetObject"]
    resources = ["${aws_s3_bucket.app_bucket.arn}/*"]

    principals {
      type = "AWS"
      identifiers = [
      aws_cloudfront_origin_access_identity.origin_access_identity.iam_arn]
    }
  }

  statement {
    actions = ["s3:ListBucket"]
    resources = [
    aws_s3_bucket.app_bucket.arn]

    principals {
      type = "AWS"
      identifiers = [
      aws_cloudfront_origin_access_identity.origin_access_identity.iam_arn]
    }
  }
}

// Create policy that will allow CloudFront to list and access files from s3 bucket
resource "aws_s3_bucket_policy" "policy_for_cloudfront" {
  bucket = aws_s3_bucket.app_bucket.id
  policy = data.aws_iam_policy_document.s3_policy.json
}

resource "aws_cloudfront_distribution" "app_distribution" {
  enabled = true
  comment = "Distribution for ${var.project_name} ${terraform.workspace}"

  origin {
    domain_name = aws_s3_bucket.app_bucket.bucket_regional_domain_name
    origin_id   = aws_s3_bucket.app_bucket.id
    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.origin_access_identity.cloudfront_access_identity_path
    }
  }

  default_root_object = "index.html"

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  viewer_certificate {
    acm_certificate_arn = var.certificate_arn
    ssl_support_method  = "sni-only"
  }

  // This is HACK. Related to https://aws.amazon.com/premiumsupport/knowledge-center/resolve-cnamealreadyexists-error/
  // Only set if we have CloudFlare entry defined. This allows to create CloudFront distribution in 2 runs.
  aliases = local.cloudflare_dns_record_exists ? [var.domain] : []

  default_cache_behavior {
    allowed_methods        = ["GET", "HEAD"]
    cached_methods         = ["GET", "HEAD"]
    target_origin_id       = aws_s3_bucket.app_bucket.id
    viewer_protocol_policy = "redirect-to-https"
    compress               = true
    min_ttl                = 0
    default_ttl            = 86400
    max_ttl                = 31536000

    forwarded_values {
      query_string = false
      cookies {
        forward = "all"
      }
    }
  }

  custom_error_response {
    error_caching_min_ttl = 0
    error_code            = 404
    response_code         = 200
    response_page_path    = "/index.html"
  }
}

output "cloudfront_distribution" {
  value = aws_cloudfront_distribution.app_distribution.id
}
