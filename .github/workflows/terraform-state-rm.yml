name: Terraform remove state

on:
  workflow_dispatch:
    inputs:
      resourceAddr:
        description: 'Terraform resource address'
        required: true

env:
  AWS_REGION: eu-central-1

jobs:
  configure_matrix:
    name: 'Configure build matrix'
    runs-on: ubuntu-latest
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Install Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 20

      - name: 'Configure build matrix'
        id: set-matrix
        run: node scripts/generate-build-matrix.js ${{ github.ref }}

  force_unlock:
    name: Remove state
    runs-on: ubuntu-latest
    needs: configure_matrix
    strategy:
      matrix: ${{ fromJson(needs.configure_matrix.outputs.matrix) }}
    steps:
      - uses: actions/checkout@v3

      - name: 'Configure AWS credentials'
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
          mask-aws-account-id: 'no'

      - name: 'Install terraform'
        uses: hashicorp/setup-terraform@v1

      - name: 'Terraform init'
        working-directory: ./infrastructure
        run: terraform init

      - name: 'Terraform select workspace'
        working-directory: ./infrastructure
        run: terraform workspace select ${{ matrix.dist }}

      - name: 'Terraform state rm'
        working-directory: ./infrastructure
        run: terraform state rm '${{ github.event.inputs.resourceAddr }}'
