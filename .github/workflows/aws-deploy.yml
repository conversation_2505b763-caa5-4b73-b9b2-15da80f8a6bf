name: Deploy to AWS

on:
  push:
    branches:
      - master
      - develop
      - dekker-dev0
      - dekker-dev1
      - dekker-dev2
      - epic/DEV-1301
      - cra

env:
  # This is latest version available on AWS lambda
  NODE_VERSION: 18
  PROJECT_PREFIX: esto-partner-v2
  AWS_REGION: eu-central-1

jobs:
  configure_matrix:
    name: 'Configure build matrix'
    runs-on: ubuntu-latest
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
    steps:
      - name: 'Checkout'
        uses: actions/checkout@v3

      - name: Install Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 20

      - name: 'Configure build matrix'
        id: set-matrix
        run: node scripts/generate-build-matrix.js ${{ github.ref }}

  build_and_deploy:
    name: 'Build and deploy ${{ matrix.dist }}'
    runs-on: ubuntu-latest
    needs: configure_matrix
    strategy:
      matrix: ${{ fromJson(needs.configure_matrix.outputs.matrix) }}
    env:
      TF_VAR_certificate_arn: ${{ matrix.certificateArn }}
      TF_VAR_domain: ${{ matrix.domain }}
      TF_VAR_cloudflare_zone_id: ${{ matrix.cloudflareZoneId }}
      TF_VAR_cloudflare_token: ${{ secrets.CLOUDFLARE_TOKEN }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Install Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 20

      - uses: pnpm/action-setup@v3
        name: Install pnpm
        with:
          version: 8
          run_install: false

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - uses: actions/cache@v3
        name: Setup pnpm cache
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install

      - name: 'Configure AWS credentials'
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
          mask-aws-account-id: 'no'

      - name: 'Install terraform'
        uses: hashicorp/setup-terraform@v1
        with:
          terraform_wrapper: false

      - name: 'Terraform init'
        working-directory: ./infrastructure
        # Some env variables are set here, as didn't find better way to reassign values
        run: |
          terraform init
          echo TF_VAR_project_name=$(echo $PROJECT_PREFIX) >> $GITHUB_ENV
          echo TF_VAR_region=$(echo $AWS_REGION) >> $GITHUB_ENV

      - name: 'Terraform select workspace'
        working-directory: ./infrastructure
        run: terraform workspace select ${{ matrix.dist }} || terraform workspace new ${{ matrix.dist }}

      - name: 'Build Application'
        run: |
          export REACT_APP_SENTRY_RELEASE=$(pnpm run print-version --silent)
          pnpm dlx env-cmd -e ${{ matrix.dist }} pnpm run build

      - name: 'Upload sentry source maps'
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          SENTRY_ORG: esto-as
          SENTRY_PROJECT: ${{ env.PROJECT_PREFIX }}
          SENTRY_ENVIRONMENT: ${{ matrix.dist }}
        run: pnpm run upload-src-maps

      - name: 'Remove source maps'
        run: |
          find build -name '*.map' -delete

      - name: 'Terraform apply'
        working-directory: ./infrastructure
        # We can only deploy in multiple runs because of https://aws.amazon.com/premiumsupport/knowledge-center/resolve-cnamealreadyexists-error/
        run: |
          terraform apply -auto-approve
          until terraform output cloudfront_alias_set | grep -q "true"; do
            terraform apply -auto-approve
          done
          echo CLOUDFRONT_DISTRIBUTION_ID=$(var=$(terraform output cloudfront_distribution); echo ${var:1:-1}) >> $GITHUB_ENV

      - name: 'Invalidate CloudFront cache'
        run: |
          aws cloudfront create-invalidation --distribution-id $CLOUDFRONT_DISTRIBUTION_ID --paths "/*"

      # - name: 'Invoke E2E workflow'
      #   uses: benc-uk/workflow-dispatch@v1
      #   with:
      #     workflow: Run E2E tests
      #     token: ${{ secrets.PERSONAL_TOKEN }}
      #     inputs: '{ "url": "https://${{ matrix.domain }}" }'
