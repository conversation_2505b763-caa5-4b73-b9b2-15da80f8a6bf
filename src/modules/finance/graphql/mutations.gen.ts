/** @generated THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */
import * as Types from '../../../shared/api/models.gen';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type CreateKlixBanklinkPaymentMutationVariables = Types.Exact<{
  applicationId?: Types.InputMaybe<Types.Scalars['Int']['input']>;
  creditAccountId?: Types.InputMaybe<Types.Scalars['Int']['input']>;
  userId?: Types.InputMaybe<Types.Scalars['Int']['input']>;
  merchantId?: Types.InputMaybe<Types.Scalars['Int']['input']>;
  amount: Types.Scalars['Float']['input'];
  redirectUrl: Types.Scalars['String']['input'];
  paymentMethodKey?: Types.InputMaybe<Types.Scalars['String']['input']>;
  paymentCategory?: Types.InputMaybe<Types.PaymentCategoryType>;
}>;

export type CreateKlixBanklinkPaymentMutation = {
  __typename?: 'Mutation';
  create_klix_banklink_payment?: {
    __typename?: 'KlixPayment';
    id: number;
    payment_url?: string | null;
    session_id: string;
    amount: number;
    status?: Types.KlixPaymentStatusType | null;
    klix_request_id: string;
    reference?: string | null;
    payer_iban?: string | null;
    payment_method_key?: string | null;
  } | null;
};

export const CreateKlixBanklinkPaymentDocument = gql`
  mutation CreateKlixBanklinkPayment(
    $applicationId: Int
    $creditAccountId: Int
    $userId: Int
    $merchantId: Int
    $amount: Float!
    $redirectUrl: String!
    $paymentMethodKey: String
    $paymentCategory: PaymentCategoryType
  ) {
    create_klix_banklink_payment(
      application_id: $applicationId
      credit_account_id: $creditAccountId
      user_id: $userId
      merchant_id: $merchantId
      amount: $amount
      redirect_url: $redirectUrl
      payment_method_key: $paymentMethodKey
      payment_category: $paymentCategory
    ) {
      id
      payment_url
      session_id
      amount
      status
      klix_request_id
      reference
      payer_iban
      payment_method_key
    }
  }
`;
export type CreateKlixBanklinkPaymentMutationFn = Apollo.MutationFunction<
  CreateKlixBanklinkPaymentMutation,
  CreateKlixBanklinkPaymentMutationVariables
>;

/**
 * __useCreateKlixBanklinkPaymentMutation__
 *
 * To run a mutation, you first call `useCreateKlixBanklinkPaymentMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateKlixBanklinkPaymentMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createKlixBanklinkPaymentMutation, { data, loading, error }] = useCreateKlixBanklinkPaymentMutation({
 *   variables: {
 *      applicationId: // value for 'applicationId'
 *      creditAccountId: // value for 'creditAccountId'
 *      userId: // value for 'userId'
 *      merchantId: // value for 'merchantId'
 *      amount: // value for 'amount'
 *      redirectUrl: // value for 'redirectUrl'
 *      paymentMethodKey: // value for 'paymentMethodKey'
 *      paymentCategory: // value for 'paymentCategory'
 *   },
 * });
 */
export function useCreateKlixBanklinkPaymentMutation(
  baseOptions?: Apollo.MutationHookOptions<
    CreateKlixBanklinkPaymentMutation,
    CreateKlixBanklinkPaymentMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    CreateKlixBanklinkPaymentMutation,
    CreateKlixBanklinkPaymentMutationVariables
  >(CreateKlixBanklinkPaymentDocument, options);
}
export type CreateKlixBanklinkPaymentMutationHookResult = ReturnType<
  typeof useCreateKlixBanklinkPaymentMutation
>;
export type CreateKlixBanklinkPaymentMutationResult =
  Apollo.MutationResult<CreateKlixBanklinkPaymentMutation>;
export type CreateKlixBanklinkPaymentMutationOptions =
  Apollo.BaseMutationOptions<
    CreateKlixBanklinkPaymentMutation,
    CreateKlixBanklinkPaymentMutationVariables
  >;
