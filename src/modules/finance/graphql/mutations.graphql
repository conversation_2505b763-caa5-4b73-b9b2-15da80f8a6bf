mutation CreateKlixBanklinkPayment(
  $applicationId: Int
  $creditAccountId: Int
  $userId: Int
  $merchantId: Int
  $amount: Float!
  $redirectUrl: String!
  $paymentMethodKey: String
  $paymentCategory: PaymentCategoryType
) {
  create_klix_banklink_payment(
    application_id: $applicationId
    credit_account_id: $creditAccountId
    user_id: $userId
    merchant_id: $merchantId
    amount: $amount
    redirect_url: $redirectUrl
    payment_method_key: $paymentMethodKey
    payment_category: $paymentCategory
  ) {
    id
    payment_url
    session_id
    amount
    status
    klix_request_id
    reference
    payer_iban
    payment_method_key
  }
}
