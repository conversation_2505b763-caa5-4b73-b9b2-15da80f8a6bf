import { But<PERSON>, <PERSON>lapse, HStack, Icon, Text, VStack } from '@chakra-ui/react';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Fi<PERSON>lertTriangle, FiChevronDown, FiChevronUp } from 'react-icons/fi';
import {
  LocizeFinanceKeys,
  LocizeNamespaces,
} from 'shared/constants/localization-keys';
import { useMerchantDetails } from 'shared/hooks/merchant';
import { useFormattedAmount } from 'shared/hooks/utils';
import { customerContacts } from 'shared/lib/env';

import {
  OVERDUE_COLORS,
  PENALTY_FEE_RATE,
} from '../constants/overdue-constants';
import { useCreateKlixPayment } from '../hooks/useCreateKlixPayment';
import { useManualTransferPaymentInfo } from '../hooks/useManualTransferPaymentInfo';

export enum OverdueBannerType {
  PENDING_INVOICE = 'pending_invoice',
  OVERDUE_WARNING = 'overdue_warning',
  OVERDUE_CRITICAL = 'overdue_critical',
}

type OverdueBannerProps = {
  unpaidAmount: number;
  dueDate?: string;
  daysOverdue?: number;
  type: OverdueBannerType;
  paymentUrl?: string;
  onPayNowClick?: () => void;
};

const getBannerConfig = (type: OverdueBannerType) => {
  switch (type) {
    case OverdueBannerType.OVERDUE_WARNING:
      return {
        status: 'warning' as const,
        bg: OVERDUE_COLORS.ORANGE_BG,
        borderColor: OVERDUE_COLORS.ORANGE_BORDER,
        iconColor: OVERDUE_COLORS.ORANGE_PRIMARY,
        buttonColorScheme: 'orange',
      };
    case OverdueBannerType.OVERDUE_CRITICAL:
      return {
        status: 'error' as const,
        bg: OVERDUE_COLORS.RED_BG,
        borderColor: OVERDUE_COLORS.RED_BORDER,
        iconColor: OVERDUE_COLORS.RED_PRIMARY,
        buttonColorScheme: 'red',
      };
    case OverdueBannerType.PENDING_INVOICE:
    default:
      return {
        status: 'info' as const,
        bg: OVERDUE_COLORS.ORANGE_BG,
        borderColor: OVERDUE_COLORS.ORANGE_BORDER,
        iconColor: OVERDUE_COLORS.ORANGE_PRIMARY,
        buttonColorScheme: 'orange',
      };
  }
};

export const OverdueBanner = ({
  unpaidAmount,
  dueDate,
  daysOverdue,
  type,
  paymentUrl,
  onPayNowClick,
}: OverdueBannerProps) => {
  const { t } = useTranslation(LocizeNamespaces.FINANCE);
  const formattedAmount = useFormattedAmount(unpaidAmount);
  const config = getBannerConfig(type);
  const [isDetailsExpanded, setIsDetailsExpanded] = useState(false);

  const { data: merchantData } = useMerchantDetails();

  const { data: paymentInfo } = useManualTransferPaymentInfo();

  const { createPayment, loading: paymentLoading } = useCreateKlixPayment();

  const handlePayNowClick = async () => {
    if (onPayNowClick) {
      onPayNowClick();
      return;
    }

    if (paymentUrl) {
      window.open(paymentUrl, '_blank');
      return;
    }

    if (merchantData?.merchant?.id) {
      try {
        await createPayment({
          merchantId: merchantData.merchant.id,
          amount: unpaidAmount,
          redirectUrl: window.location.origin + '/finance',
        });
      } catch (error) {
        console.error('Payment creation failed:', error);
        console.dir('error', error);

        window.location.href = '/payment/invoice/';
      }
    } else {
      window.location.href = '/payment/invoice/';
    }
  };

  const bannerContent = useMemo(() => {
    const daysText = daysOverdue
      ? ` (${daysOverdue} ${t(LocizeFinanceKeys.DAYS_OVERDUE)})`
      : '';

    return {
      title: `${t(LocizeFinanceKeys.OUTSTANDING_BALANCE)}: ${formattedAmount}${daysText}`,
      description: t(LocizeFinanceKeys.PLEASE_SETTLE_TO_AVOID_INTERRUPTION),
    };
  }, [formattedAmount, daysOverdue, t]);

  if (unpaidAmount <= 0) {
    return null;
  }

  return (
    <VStack
      bg={config.bg}
      borderColor={config.borderColor}
      borderWidth="1px"
      borderRadius="8px"
      p={3}
      spacing={2}
      align="stretch"
    >
      <HStack spacing={4} align="center" justify="space-between">
        <HStack spacing={3} flex="1">
          <Icon as={FiAlertTriangle} color={config.iconColor} boxSize={5} />
          <VStack align="flex-start" spacing={0}>
            <Text fontWeight="semibold" fontSize="md" color="gray.800">
              {bannerContent.title}
            </Text>
            <Text fontSize="sm" color="gray.600">
              {bannerContent.description}
            </Text>
          </VStack>
        </HStack>

        <HStack spacing={2}>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsDetailsExpanded(!isDetailsExpanded)}
            rightIcon={
              <Icon as={isDetailsExpanded ? FiChevronUp : FiChevronDown} />
            }
          >
            {isDetailsExpanded
              ? t(LocizeFinanceKeys.HIDE_DETAILS)
              : t(LocizeFinanceKeys.SHOW_PAYMENT_DETAILS)}
          </Button>
          <Button
            colorScheme={config.buttonColorScheme}
            size="md"
            onClick={handlePayNowClick}
            isLoading={paymentLoading}
          >
            {t(LocizeFinanceKeys.PAY_NOW_BUTTON)}
          </Button>
        </HStack>
      </HStack>

      <Collapse in={isDetailsExpanded} animateOpacity>
        <VStack
          align="stretch"
          spacing={2}
          pt={2}
          borderTop="1px solid"
          borderTopColor="gray.200"
        >
          <VStack align="stretch" spacing={2}>
            <Text fontWeight="semibold" fontSize="sm">
              {t(LocizeFinanceKeys.ESTO_PAYMENT_INFO_TITLE)}
            </Text>
            <VStack align="stretch" spacing={1} fontSize="xs" color="gray.600">
              {paymentInfo?.iban && (
                <Text>
                  <Text as="span" fontWeight="medium">
                    IBAN:
                  </Text>{' '}
                  {paymentInfo.iban}
                </Text>
              )}
              {paymentInfo?.beneficiary_name && (
                <Text>
                  <Text as="span" fontWeight="medium">
                    {t(LocizeFinanceKeys.BENEFICIARY_LABEL)}:
                  </Text>{' '}
                  {paymentInfo.beneficiary_name}
                </Text>
              )}
              {paymentInfo?.swift && (
                <Text>
                  <Text as="span" fontWeight="medium">
                    SWIFT:
                  </Text>{' '}
                  {paymentInfo.swift}
                </Text>
              )}
            </VStack>
          </VStack>

          <VStack align="stretch" spacing={2}>
            <Text fontWeight="semibold" fontSize="sm">
              {t(LocizeFinanceKeys.ESTO_CONTACT_INFO_TITLE)}
            </Text>
            <VStack align="stretch" spacing={1} fontSize="xs" color="gray.600">
              {customerContacts.email && (
                <Text>
                  <Text as="span" fontWeight="medium">
                    {t(LocizeFinanceKeys.EMAIL_LABEL)}:
                  </Text>{' '}
                  {customerContacts.email}
                </Text>
              )}
              {customerContacts.phone && (
                <Text>
                  <Text as="span" fontWeight="medium">
                    {t(LocizeFinanceKeys.PHONE_LABEL)}:
                  </Text>{' '}
                  {customerContacts.phone}
                </Text>
              )}
            </VStack>
          </VStack>

          {(type === OverdueBannerType.OVERDUE_WARNING ||
            type === OverdueBannerType.OVERDUE_CRITICAL) && (
            <Text fontSize="xs" color="gray.600">
              <Text as="span" fontWeight="medium">
                {t(LocizeFinanceKeys.PENALTY_FEE_LABEL)}:
              </Text>{' '}
              {PENALTY_FEE_RATE}
            </Text>
          )}

          {dueDate && (
            <Text fontSize="xs" color="gray.600">
              <Text as="span" fontWeight="medium">
                Due Date:
              </Text>{' '}
              {dueDate}
            </Text>
          )}
        </VStack>
      </Collapse>
    </VStack>
  );
};
