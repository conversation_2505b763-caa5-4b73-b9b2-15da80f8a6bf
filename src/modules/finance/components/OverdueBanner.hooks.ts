import { differenceInDays, isAfter, startOfDay } from 'date-fns';
import { useMemo } from 'react';

import { OVERDUE_THRESHOLDS } from '../constants/overdue-constants';
import { OverdueBannerType } from './OverdueBanner';

type UseOverdueBannerParams = {
  unpaidAmount: number;
  dueDate?: string;
};

type OverdueBannerData = {
  shouldShow: boolean;
  type: OverdueBannerType;
  unpaidAmount: number;
  daysOverdue?: number;
  dueDate?: string;
} | null;

export const useOverdueBanner = ({
  unpaidAmount,
  dueDate,
}: UseOverdueBannerParams): OverdueBannerData => {
  return useMemo(() => {
    if (!unpaidAmount || unpaidAmount <= 0) {
      return null;
    }

    const today = startOfDay(new Date());
    const currentDay = today.getDate();

    if (!dueDate) {
      if (
        currentDay >= OVERDUE_THRESHOLDS.PENDING_START_DAY &&
        currentDay < OVERDUE_THRESHOLDS.PENDING_END_DAY
      ) {
        return {
          shouldShow: true,
          type: OverdueBannerType.PENDING_INVOICE,
          unpaidAmount,
        };
      }

      if (
        currentDay >= OVERDUE_THRESHOLDS.WARNING_START_DAY &&
        currentDay < OVERDUE_THRESHOLDS.WARNING_END_DAY
      ) {
        return {
          shouldShow: true,
          type: OverdueBannerType.OVERDUE_WARNING,
          unpaidAmount,
        };
      }

      if (currentDay >= OVERDUE_THRESHOLDS.CRITICAL_START_DAY) {
        return {
          shouldShow: true,
          type: OverdueBannerType.OVERDUE_CRITICAL,
          unpaidAmount,
        };
      }

      return null;
    }

    const dueDateObj = startOfDay(new Date(dueDate));
    const isOverdue = isAfter(today, dueDateObj);
    const daysOverdue = isOverdue ? differenceInDays(today, dueDateObj) : 0;

    if (isOverdue) {
      if (daysOverdue >= OVERDUE_THRESHOLDS.OVERDUE_CRITICAL_DAYS) {
        return {
          shouldShow: true,
          type: OverdueBannerType.OVERDUE_CRITICAL,
          unpaidAmount,
          daysOverdue,
          dueDate,
        };
      }

      if (daysOverdue >= OVERDUE_THRESHOLDS.OVERDUE_WARNING_DAYS) {
        return {
          shouldShow: true,
          type: OverdueBannerType.OVERDUE_WARNING,
          unpaidAmount,
          daysOverdue,
          dueDate,
        };
      }

      return {
        shouldShow: true,
        type: OverdueBannerType.OVERDUE_WARNING,
        unpaidAmount,
        daysOverdue,
        dueDate,
      };
    }

    if (currentDay >= OVERDUE_THRESHOLDS.PENDING_START_DAY) {
      return {
        shouldShow: true,
        type: OverdueBannerType.PENDING_INVOICE,
        unpaidAmount,
        dueDate,
      };
    }

    return null;
  }, [unpaidAmount, dueDate]);
};
