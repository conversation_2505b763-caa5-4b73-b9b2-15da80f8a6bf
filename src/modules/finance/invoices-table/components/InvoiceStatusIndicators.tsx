import { Badge, Button, HStack } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import {
  LocizeFinanceKeys,
  LocizeNamespaces,
} from 'shared/constants/localization-keys';

import {
  isInvoiceNew,
  OVERDUE_COLORS,
} from '../../constants/overdue-constants';

type Props = {
  invoiceId: number;
  type: string;
  unsettledAmount: number;
  createdAt?: string;
};

export const InvoiceStatusIndicators = ({
  unsettledAmount,
  createdAt,
}: Props) => {
  const { t } = useTranslation(LocizeNamespaces.FINANCE);

  const isNew = isInvoiceNew(createdAt);

  const isUnsettled = unsettledAmount > 0;

  const badgeColors = {
    green: OVERDUE_COLORS.GREEN_PRIMARY,
    blue: OVERDUE_COLORS.BLUE_PRIMARY,
  };

  const newBadgeColor = Math.random() > 0.5 ? 'green' : 'blue';

  return (
    <HStack spacing={2}>
      {isNew && (
        <Badge
          bg={badgeColors[newBadgeColor]}
          color="white"
          fontSize="xs"
          px={2}
          py={1}
          borderRadius="md"
          fontWeight="600"
        >
          {t(LocizeFinanceKeys.INVOICES_TABLE_STATUS_NEW)}
        </Badge>
      )}

      {isUnsettled && (
        <Button
          size="xs"
          bg={OVERDUE_COLORS.RED_PRIMARY}
          color="white"
          _hover={{ bg: OVERDUE_COLORS.RED_HOVER }}
          _active={{ bg: OVERDUE_COLORS.RED_ACTIVE }}
          width="16px"
          height="16px"
          minWidth="16px"
          minHeight="16px"
          borderRadius="50%"
          cursor="default"
          style={{
            zIndex: -1,
          }}
          _focus={{ boxShadow: 'none' }}
          p={0}
        />
      )}
    </HStack>
  );
};
