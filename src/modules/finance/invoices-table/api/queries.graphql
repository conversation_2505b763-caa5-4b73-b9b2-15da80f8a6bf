query InvoicesList(
  $page: Int
  $limit: Int
  $orderBy: OrderBy
  $direction: Direction
  $merchantId: Int
) {
  invoices(
    page: $page
    limit: $limit
    orderBy: $orderBy
    direction: $direction
    merchant_id: $merchantId
  ) {
    data {
      id
      merchant_id
      type
      invoice_nr
      total_amount
      due_at
      url
      from_date
      merchant_invoice {
        invoice_id
        paid
      }
    }
    has_more_pages
  }
}
