// Overdue banner and invoice status constants
export const OVERDUE_THRESHOLDS = {
  // Days of the month for pending invoice display
  PENDING_START_DAY: 2,
  PENDING_END_DAY: 15,

  // Days of the month for warning display
  WARNING_START_DAY: 15,
  WARNING_END_DAY: 30,

  // Days of the month for critical display
  CRITICAL_START_DAY: 30,

  // Days overdue thresholds
  OVERDUE_WARNING_DAYS: 15,
  OVERDUE_CRITICAL_DAYS: 30,

  // New invoice threshold (days)
  NEW_INVOICE_DAYS: 7,
} as const;

export const OVERDUE_COLORS = {
  // Red colors for unsettled/critical
  RED_PRIMARY: '#DC2626',
  RED_HOVER: '#B91C1C',
  RED_ACTIVE: '#991B1B',

  // Orange colors for warning
  ORANGE_PRIMARY: '#EA580C',
  ORANGE_BG: '#FEF7ED',
  ORANGE_BORDER: '#FED7AA',

  // Red colors for critical background
  RED_BG: '#FEF2F2',
  RED_BORDER: '#FECACA',

  // New invoice badge colors
  GREEN_PRIMARY: '#10B981',
  BLUE_PRIMARY: '#3B82F6',
} as const;

export const PENALTY_FEE_RATE = '0.05%' as const;

// Utility functions
export const isInvoiceNew = (createdAt?: string): boolean => {
  if (!createdAt) return false;
  const createdDate = new Date(createdAt);
  const thresholdDate = new Date(
    Date.now() - OVERDUE_THRESHOLDS.NEW_INVOICE_DAYS * 24 * 60 * 60 * 1000,
  );
  return createdDate > thresholdDate;
};
