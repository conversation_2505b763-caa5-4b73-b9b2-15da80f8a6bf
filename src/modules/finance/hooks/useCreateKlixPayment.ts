import { useMutation } from '@apollo/client';

import { CREATE_KLIX_BANKLINK_PAYMENT_MUTATION } from '../graphql';

export interface CreateKlixPaymentVariables {
  applicationId?: number;
  creditAccountId?: number;
  userId?: number;
  merchantId?: number;
  amount: number;
  redirectUrl: string;
  paymentMethodKey?: string;
  paymentCategory?: string;
}

export interface KlixPaymentResponse {
  id: number;
  payment_url: string;
  session_id: string;
  amount: number;
  status: string;
  klix_request_id: string;
  reference?: string;
  payer_iban?: string;
  payment_method_key?: string;
}

export const useCreateKlixPayment = () => {
  const [createKlixPayment, { loading, error, data }] = useMutation<
    { create_klix_banklink_payment: KlixPaymentResponse },
    CreateKlixPaymentVariables
  >(CREATE_KLIX_BANKLINK_PAYMENT_MUTATION);

  const createPayment = async (variables: CreateKlixPaymentVariables) => {
    try {
      const result = await createKlixPayment({
        variables,
      });

      if (result.data?.create_klix_banklink_payment?.payment_url) {
        window.location.href =
          result.data.create_klix_banklink_payment.payment_url;
      }

      return result.data?.create_klix_banklink_payment;
    } catch (err) {
      console.error('Failed to create Klix payment:', err);
      throw err;
    }
  };

  return {
    createPayment,
    loading,
    error,
    data: data?.create_klix_banklink_payment,
  };
};
