import { useQuery } from '@apollo/client';

import { PAYMENT_METHODS_QUERY } from '../graphql';

export enum PaymentMethodType {
  BANK_WIRE = 'BANK_WIRE',
}

export enum PaymentMethodKeys {
  MAIN = 'MAIN',
  LEGACY = 'LEGACY',
}

export const useManualTransferPaymentInfo = () => {
  const countryCode = 'EE';

  const { data, loading, error } = useQuery(PAYMENT_METHODS_QUERY, {
    variables: {
      countryCode,
    },
  });

  const processedData = data?.payment_methods
    ? (() => {
        const wirePaymentMethods = data.payment_methods.filter(
          (method: any) => method?.type === PaymentMethodType.BANK_WIRE,
        );

        if (!wirePaymentMethods?.length) return null;

        const wireMethodWithIban = wirePaymentMethods.find(
          (method: any) => method?.iban,
        );

        return wireMethodWithIban || wirePaymentMethods[0];
      })()
    : null;

  return {
    data: processedData,
    loading,
    error,
  };
};
