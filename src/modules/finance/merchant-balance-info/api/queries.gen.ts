/** @generated THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */
import * as Types from '../../../../shared/api/models.gen';

import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type MerchantBalanceQueryVariables = Types.Exact<{
  merchantId: Types.Scalars['Int']['input'];
  balanceAt?: Types.InputMaybe<Types.Scalars['String']['input']>;
}>;

export type MerchantBalanceQuery = {
  __typename?: 'Query';
  balance?: {
    __typename?: 'UnpaidMerchant';
    id: number;
    pending_principal: number;
    pending_bonus: number;
    unpaid_amount: number;
  } | null;
};

export type MerchantRecentInvoiceQueryVariables = Types.Exact<{
  merchantId: Types.Scalars['Int']['input'];
}>;

export type MerchantRecentInvoiceQuery = {
  __typename?: 'Query';
  invoices?: {
    __typename?: 'invoicePagination';
    data?: Array<{
      __typename?: 'Invoice';
      id: number;
      due_at: string;
      total_amount: number;
    } | null> | null;
  } | null;
};

export const MerchantBalanceDocument = gql`
  query MerchantBalance($merchantId: Int!, $balanceAt: String) {
    balance: merchant_balance(
      merchant_id: $merchantId
      balance_at: $balanceAt
    ) {
      id
      pending_principal
      pending_bonus
      unpaid_amount
    }
  }
`;

/**
 * __useMerchantBalanceQuery__
 *
 * To run a query within a React component, call `useMerchantBalanceQuery` and pass it any options that fit your needs.
 * When your component renders, `useMerchantBalanceQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useMerchantBalanceQuery({
 *   variables: {
 *      merchantId: // value for 'merchantId'
 *      balanceAt: // value for 'balanceAt'
 *   },
 * });
 */
export function useMerchantBalanceQuery(
  baseOptions: Apollo.QueryHookOptions<
    MerchantBalanceQuery,
    MerchantBalanceQueryVariables
  > &
    (
      | { variables: MerchantBalanceQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<MerchantBalanceQuery, MerchantBalanceQueryVariables>(
    MerchantBalanceDocument,
    options,
  );
}
export function useMerchantBalanceLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    MerchantBalanceQuery,
    MerchantBalanceQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    MerchantBalanceQuery,
    MerchantBalanceQueryVariables
  >(MerchantBalanceDocument, options);
}
export function useMerchantBalanceSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<
        MerchantBalanceQuery,
        MerchantBalanceQueryVariables
      >,
) {
  const options =
    baseOptions === Apollo.skipToken
      ? baseOptions
      : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    MerchantBalanceQuery,
    MerchantBalanceQueryVariables
  >(MerchantBalanceDocument, options);
}
export type MerchantBalanceQueryHookResult = ReturnType<
  typeof useMerchantBalanceQuery
>;
export type MerchantBalanceLazyQueryHookResult = ReturnType<
  typeof useMerchantBalanceLazyQuery
>;
export type MerchantBalanceSuspenseQueryHookResult = ReturnType<
  typeof useMerchantBalanceSuspenseQuery
>;
export type MerchantBalanceQueryResult = Apollo.QueryResult<
  MerchantBalanceQuery,
  MerchantBalanceQueryVariables
>;
export const MerchantRecentInvoiceDocument = gql`
  query MerchantRecentInvoice($merchantId: Int!) {
    invoices(
      merchant_id: $merchantId
      limit: 1
      direction: desc
      orderBy: created_at
    ) {
      data {
        id
        due_at
        total_amount
      }
    }
  }
`;

/**
 * __useMerchantRecentInvoiceQuery__
 *
 * To run a query within a React component, call `useMerchantRecentInvoiceQuery` and pass it any options that fit your needs.
 * When your component renders, `useMerchantRecentInvoiceQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useMerchantRecentInvoiceQuery({
 *   variables: {
 *      merchantId: // value for 'merchantId'
 *   },
 * });
 */
export function useMerchantRecentInvoiceQuery(
  baseOptions: Apollo.QueryHookOptions<
    MerchantRecentInvoiceQuery,
    MerchantRecentInvoiceQueryVariables
  > &
    (
      | { variables: MerchantRecentInvoiceQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    MerchantRecentInvoiceQuery,
    MerchantRecentInvoiceQueryVariables
  >(MerchantRecentInvoiceDocument, options);
}
export function useMerchantRecentInvoiceLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    MerchantRecentInvoiceQuery,
    MerchantRecentInvoiceQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    MerchantRecentInvoiceQuery,
    MerchantRecentInvoiceQueryVariables
  >(MerchantRecentInvoiceDocument, options);
}
export function useMerchantRecentInvoiceSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<
        MerchantRecentInvoiceQuery,
        MerchantRecentInvoiceQueryVariables
      >,
) {
  const options =
    baseOptions === Apollo.skipToken
      ? baseOptions
      : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    MerchantRecentInvoiceQuery,
    MerchantRecentInvoiceQueryVariables
  >(MerchantRecentInvoiceDocument, options);
}
export type MerchantRecentInvoiceQueryHookResult = ReturnType<
  typeof useMerchantRecentInvoiceQuery
>;
export type MerchantRecentInvoiceLazyQueryHookResult = ReturnType<
  typeof useMerchantRecentInvoiceLazyQuery
>;
export type MerchantRecentInvoiceSuspenseQueryHookResult = ReturnType<
  typeof useMerchantRecentInvoiceSuspenseQuery
>;
export type MerchantRecentInvoiceQueryResult = Apollo.QueryResult<
  MerchantRecentInvoiceQuery,
  MerchantRecentInvoiceQueryVariables
>;
