query MerchantBalance($merchantId: Int!, $balanceAt: String) {
  balance: merchant_balance(merchant_id: $merchantId, balance_at: $balanceAt) {
    id
    pending_principal
    pending_bonus
    unpaid_amount
  }
}

query MerchantRecentInvoice($merchantId: Int!) {
  invoices(
    merchant_id: $merchantId
    limit: 1
    direction: desc
    orderBy: created_at
  ) {
    data {
      id
      due_at
      total_amount
    }
  }
}
