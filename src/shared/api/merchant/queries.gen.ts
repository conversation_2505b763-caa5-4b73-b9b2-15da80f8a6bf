/** @generated THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */
import * as Types from '../models.gen';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type CalcSkinBasicFragment = {
  __typename?: 'MerchantCalculatorSkin';
  merchant_id: number;
  main: string;
  secondary: string;
  text: string;
  period: string;
  active_period: string;
  period_text: string;
  monthly_text: string;
  button: string;
  button_text: string;
};

export type CampaignBasicFragment = {
  __typename?: 'MerchantCampaign';
  is_active: boolean;
  regular_hp_enabled: boolean;
  converting_schedule_enabled: boolean;
  converting_schedule_name?: string | null;
  converting_schedule_logo_url?: string | null;
  converting_schedule_net_total_min: number;
  converting_schedule_net_total_max: number;
  converting_schedule_months?: number | null;
  converting_schedule_regular_months?: number | null;
  converting_schedule_reverse_kickback_pct: number;
  pay_later_enabled: boolean;
  pay_later_name?: string | null;
  pay_later_logo_url?: string | null;
  pay_later_net_total_min: number;
  pay_later_net_total_max: number;
  pay_later_reverse_kickback_pct: number;
  esto_pay_enabled: boolean;
  esto_pay_name?: string | null;
  esto_pay_logo_url?: string | null;
  esto_pay_net_total_min: number;
  esto_pay_net_total_max: number;
  fixed_annual_pct_rate?: number | null;
  fixed_management_fee?: number | null;
  fixed_contract_fee?: number | null;
  converting_schedule_fixed_contract_fee?: number | null;
  direct_payment_gateways?: Array<{
    __typename?: 'MerchantDirectPaymentGateway';
    enabled: boolean;
    provider: string;
    fee_fixed: number;
    fee_pct: number;
    fee_total_min: number;
    fee_total_max: number;
  } | null> | null;
};

export type MerchantBasicFragment = {
  __typename?: 'Merchant';
  id: number;
  shop_id: string;
  name: string;
  return_url?: string | null;
  notification_url?: string | null;
  cancel_url?: string | null;
  home_url?: string | null;
  logo_path?: string | null;
  registry_code: string;
  phone: string;
  address: string;
  iban: string;
  beneficiary_name: string;
  cashier_loyalty_enabled: boolean;
  email: string;
  secret_key?: string | null;
  settings?: {
    __typename?: 'MerchantSettings';
    merchant_financing_pct: number;
    net_total_min: number;
    net_total_max: number;
    can_create_small_loan: boolean;
    can_create_dynamic_loan: boolean;
    can_enable_special_settings: boolean;
    can_see_eligibility_status: boolean;
    buyback_guarantee_days: number;
    buyback_discount_pct: number;
    bonus_pct: number;
    bonus_type?: Types.MerchantSettingsBonusType | null;
    reverse_kickback_pct: number;
    max_amount_id_verification_not_required: number;
    min_months_period: number;
    max_months_period: number;
    cashier_bonus_pct: number;
  } | null;
  campaign?: {
    __typename?: 'MerchantCampaign';
    is_active: boolean;
    regular_hp_enabled: boolean;
    converting_schedule_enabled: boolean;
    converting_schedule_name?: string | null;
    converting_schedule_logo_url?: string | null;
    converting_schedule_net_total_min: number;
    converting_schedule_net_total_max: number;
    converting_schedule_months?: number | null;
    converting_schedule_regular_months?: number | null;
    converting_schedule_reverse_kickback_pct: number;
    pay_later_enabled: boolean;
    pay_later_name?: string | null;
    pay_later_logo_url?: string | null;
    pay_later_net_total_min: number;
    pay_later_net_total_max: number;
    pay_later_reverse_kickback_pct: number;
    esto_pay_enabled: boolean;
    esto_pay_name?: string | null;
    esto_pay_logo_url?: string | null;
    esto_pay_net_total_min: number;
    esto_pay_net_total_max: number;
    fixed_annual_pct_rate?: number | null;
    fixed_management_fee?: number | null;
    fixed_contract_fee?: number | null;
    converting_schedule_fixed_contract_fee?: number | null;
    direct_payment_gateways?: Array<{
      __typename?: 'MerchantDirectPaymentGateway';
      enabled: boolean;
      provider: string;
      fee_fixed: number;
      fee_pct: number;
      fee_total_min: number;
      fee_total_max: number;
    } | null> | null;
  } | null;
  calculator_skin?: {
    __typename?: 'MerchantCalculatorSkin';
    merchant_id: number;
    main: string;
    secondary: string;
    text: string;
    period: string;
    active_period: string;
    period_text: string;
    monthly_text: string;
    button: string;
    button_text: string;
  } | null;
};

export type MerchantStoreBasicFragment = {
  __typename?: 'MerchantStore';
  id: number;
  name: string;
  cashiers?: Array<{
    __typename?: 'User';
    id: number;
    email?: string | null;
    profile?: {
      __typename?: 'UserProfile';
      first_name?: string | null;
      last_name?: string | null;
    } | null;
  } | null> | null;
};

export type MerchantQueryVariables = Types.Exact<{
  merchantId: Types.Scalars['Int']['input'];
}>;

export type MerchantQuery = {
  __typename?: 'Query';
  merchant?: {
    __typename?: 'Merchant';
    id: number;
    shop_id: string;
    name: string;
    return_url?: string | null;
    notification_url?: string | null;
    cancel_url?: string | null;
    home_url?: string | null;
    logo_path?: string | null;
    registry_code: string;
    phone: string;
    address: string;
    iban: string;
    beneficiary_name: string;
    cashier_loyalty_enabled: boolean;
    email: string;
    secret_key?: string | null;
    users?: Array<{
      __typename?: 'MerchantUser';
      id: number;
      merchant_permission_bits: number;
      send_emails: number;
      email?: string | null;
      profile?: {
        __typename?: 'UserProfile';
        first_name?: string | null;
        last_name?: string | null;
      } | null;
    } | null> | null;
    stores?: Array<{
      __typename?: 'MerchantStore';
      id: number;
      name: string;
      cashiers?: Array<{
        __typename?: 'User';
        id: number;
        email?: string | null;
        profile?: {
          __typename?: 'UserProfile';
          first_name?: string | null;
          last_name?: string | null;
        } | null;
      } | null> | null;
    } | null> | null;
    settings?: {
      __typename?: 'MerchantSettings';
      merchant_financing_pct: number;
      net_total_min: number;
      net_total_max: number;
      can_create_small_loan: boolean;
      can_create_dynamic_loan: boolean;
      can_enable_special_settings: boolean;
      can_see_eligibility_status: boolean;
      buyback_guarantee_days: number;
      buyback_discount_pct: number;
      bonus_pct: number;
      bonus_type?: Types.MerchantSettingsBonusType | null;
      reverse_kickback_pct: number;
      max_amount_id_verification_not_required: number;
      min_months_period: number;
      max_months_period: number;
      cashier_bonus_pct: number;
    } | null;
    campaign?: {
      __typename?: 'MerchantCampaign';
      is_active: boolean;
      regular_hp_enabled: boolean;
      converting_schedule_enabled: boolean;
      converting_schedule_name?: string | null;
      converting_schedule_logo_url?: string | null;
      converting_schedule_net_total_min: number;
      converting_schedule_net_total_max: number;
      converting_schedule_months?: number | null;
      converting_schedule_regular_months?: number | null;
      converting_schedule_reverse_kickback_pct: number;
      pay_later_enabled: boolean;
      pay_later_name?: string | null;
      pay_later_logo_url?: string | null;
      pay_later_net_total_min: number;
      pay_later_net_total_max: number;
      pay_later_reverse_kickback_pct: number;
      esto_pay_enabled: boolean;
      esto_pay_name?: string | null;
      esto_pay_logo_url?: string | null;
      esto_pay_net_total_min: number;
      esto_pay_net_total_max: number;
      fixed_annual_pct_rate?: number | null;
      fixed_management_fee?: number | null;
      fixed_contract_fee?: number | null;
      converting_schedule_fixed_contract_fee?: number | null;
      direct_payment_gateways?: Array<{
        __typename?: 'MerchantDirectPaymentGateway';
        enabled: boolean;
        provider: string;
        fee_fixed: number;
        fee_pct: number;
        fee_total_min: number;
        fee_total_max: number;
      } | null> | null;
    } | null;
    calculator_skin?: {
      __typename?: 'MerchantCalculatorSkin';
      merchant_id: number;
      main: string;
      secondary: string;
      text: string;
      period: string;
      active_period: string;
      period_text: string;
      monthly_text: string;
      button: string;
      button_text: string;
    } | null;
  } | null;
};

export type MerchantInviteQueryVariables = Types.Exact<{
  inviteHash: Types.Scalars['String']['input'];
}>;

export type MerchantInviteQuery = {
  __typename?: 'Query';
  merchant_invite?: {
    __typename?: 'MerchantInvite';
    merchant_id: number;
    hash: string;
    permission_bits: number;
    email: string;
  } | null;
};

export type PricingQueryVariables = Types.Exact<{
  keys:
    | Array<Types.InputMaybe<Types.Scalars['String']['input']>>
    | Types.InputMaybe<Types.Scalars['String']['input']>;
}>;

export type PricingQuery = {
  __typename?: 'Query';
  pricing?: Array<{
    __typename?: 'Pricing';
    key: string;
    value: string;
    type: string;
  } | null> | null;
};

export type UpdateMerchantMutationVariables = Types.Exact<{
  merchantId: Types.Scalars['Int']['input'];
  name: Types.Scalars['String']['input'];
  registry_code: Types.Scalars['String']['input'];
  address: Types.Scalars['String']['input'];
  phone: Types.Scalars['String']['input'];
  email: Types.Scalars['String']['input'];
  iban: Types.Scalars['String']['input'];
  beneficiary_name: Types.Scalars['String']['input'];
  return_url?: Types.InputMaybe<Types.Scalars['String']['input']>;
  notification_url?: Types.InputMaybe<Types.Scalars['String']['input']>;
  cancel_url?: Types.InputMaybe<Types.Scalars['String']['input']>;
  home_url?: Types.InputMaybe<Types.Scalars['String']['input']>;
}>;

export type UpdateMerchantMutation = {
  __typename?: 'Mutation';
  merchant?: {
    __typename?: 'Merchant';
    id: number;
    shop_id: string;
    name: string;
    return_url?: string | null;
    notification_url?: string | null;
    cancel_url?: string | null;
    home_url?: string | null;
    logo_path?: string | null;
    registry_code: string;
    phone: string;
    address: string;
    iban: string;
    beneficiary_name: string;
    cashier_loyalty_enabled: boolean;
    email: string;
    secret_key?: string | null;
    settings?: {
      __typename?: 'MerchantSettings';
      merchant_financing_pct: number;
      net_total_min: number;
      net_total_max: number;
      can_create_small_loan: boolean;
      can_create_dynamic_loan: boolean;
      can_enable_special_settings: boolean;
      can_see_eligibility_status: boolean;
      buyback_guarantee_days: number;
      buyback_discount_pct: number;
      bonus_pct: number;
      bonus_type?: Types.MerchantSettingsBonusType | null;
      reverse_kickback_pct: number;
      max_amount_id_verification_not_required: number;
      min_months_period: number;
      max_months_period: number;
      cashier_bonus_pct: number;
    } | null;
    campaign?: {
      __typename?: 'MerchantCampaign';
      is_active: boolean;
      regular_hp_enabled: boolean;
      converting_schedule_enabled: boolean;
      converting_schedule_name?: string | null;
      converting_schedule_logo_url?: string | null;
      converting_schedule_net_total_min: number;
      converting_schedule_net_total_max: number;
      converting_schedule_months?: number | null;
      converting_schedule_regular_months?: number | null;
      converting_schedule_reverse_kickback_pct: number;
      pay_later_enabled: boolean;
      pay_later_name?: string | null;
      pay_later_logo_url?: string | null;
      pay_later_net_total_min: number;
      pay_later_net_total_max: number;
      pay_later_reverse_kickback_pct: number;
      esto_pay_enabled: boolean;
      esto_pay_name?: string | null;
      esto_pay_logo_url?: string | null;
      esto_pay_net_total_min: number;
      esto_pay_net_total_max: number;
      fixed_annual_pct_rate?: number | null;
      fixed_management_fee?: number | null;
      fixed_contract_fee?: number | null;
      converting_schedule_fixed_contract_fee?: number | null;
      direct_payment_gateways?: Array<{
        __typename?: 'MerchantDirectPaymentGateway';
        enabled: boolean;
        provider: string;
        fee_fixed: number;
        fee_pct: number;
        fee_total_min: number;
        fee_total_max: number;
      } | null> | null;
    } | null;
    calculator_skin?: {
      __typename?: 'MerchantCalculatorSkin';
      merchant_id: number;
      main: string;
      secondary: string;
      text: string;
      period: string;
      active_period: string;
      period_text: string;
      monthly_text: string;
      button: string;
      button_text: string;
    } | null;
  } | null;
};

export type CreateMerchantStoreMutationVariables = Types.Exact<{
  merchantId: Types.Scalars['Int']['input'];
  name: Types.Scalars['String']['input'];
}>;

export type CreateMerchantStoreMutation = {
  __typename?: 'Mutation';
  store?: {
    __typename?: 'MerchantStore';
    id: number;
    name: string;
    cashiers?: Array<{
      __typename?: 'User';
      id: number;
      email?: string | null;
      profile?: {
        __typename?: 'UserProfile';
        first_name?: string | null;
        last_name?: string | null;
      } | null;
    } | null> | null;
  } | null;
};

export type UpdateMerchantStoreMutationVariables = Types.Exact<{
  storeId: Types.Scalars['Int']['input'];
  name: Types.Scalars['String']['input'];
}>;

export type UpdateMerchantStoreMutation = {
  __typename?: 'Mutation';
  store?: {
    __typename?: 'MerchantStore';
    id: number;
    name: string;
    cashiers?: Array<{
      __typename?: 'User';
      id: number;
      email?: string | null;
      profile?: {
        __typename?: 'UserProfile';
        first_name?: string | null;
        last_name?: string | null;
      } | null;
    } | null> | null;
  } | null;
};

export type UpdateMerchantUserPermissionsMutationVariables = Types.Exact<{
  merchantId: Types.Scalars['Int']['input'];
  userId: Types.Scalars['Int']['input'];
  permissionBits: Types.Scalars['Int']['input'];
}>;

export type UpdateMerchantUserPermissionsMutation = {
  __typename?: 'Mutation';
  update_merchant_user_permissions: boolean;
};

export type DetachUserMerchantMutationVariables = Types.Exact<{
  merchantId: Types.Scalars['Int']['input'];
  userId: Types.Scalars['Int']['input'];
}>;

export type DetachUserMerchantMutation = {
  __typename?: 'Mutation';
  handle_user_merchant: boolean;
};

export type DeleteMerchantStoreMutationVariables = Types.Exact<{
  storeId: Types.Scalars['Int']['input'];
}>;

export type DeleteMerchantStoreMutation = {
  __typename?: 'Mutation';
  deleted: boolean;
};

export type UpdateMerchantPaymentMethodsMutationVariables = Types.Exact<{
  merchantId: Types.Scalars['Int']['input'];
  regularHpEnalbed: Types.Scalars['Boolean']['input'];
  convertingScheduleEnabled: Types.Scalars['Boolean']['input'];
  payLaterEnabled: Types.Scalars['Boolean']['input'];
  estoPayEnabled: Types.Scalars['Boolean']['input'];
}>;

export type UpdateMerchantPaymentMethodsMutation = {
  __typename?: 'Mutation';
  campaign?: {
    __typename?: 'MerchantCampaign';
    is_active: boolean;
    regular_hp_enabled: boolean;
    converting_schedule_enabled: boolean;
    converting_schedule_name?: string | null;
    converting_schedule_logo_url?: string | null;
    converting_schedule_net_total_min: number;
    converting_schedule_net_total_max: number;
    converting_schedule_months?: number | null;
    converting_schedule_regular_months?: number | null;
    converting_schedule_reverse_kickback_pct: number;
    pay_later_enabled: boolean;
    pay_later_name?: string | null;
    pay_later_logo_url?: string | null;
    pay_later_net_total_min: number;
    pay_later_net_total_max: number;
    pay_later_reverse_kickback_pct: number;
    esto_pay_enabled: boolean;
    esto_pay_name?: string | null;
    esto_pay_logo_url?: string | null;
    esto_pay_net_total_min: number;
    esto_pay_net_total_max: number;
    fixed_annual_pct_rate?: number | null;
    fixed_management_fee?: number | null;
    fixed_contract_fee?: number | null;
    converting_schedule_fixed_contract_fee?: number | null;
    direct_payment_gateways?: Array<{
      __typename?: 'MerchantDirectPaymentGateway';
      enabled: boolean;
      provider: string;
      fee_fixed: number;
      fee_pct: number;
      fee_total_min: number;
      fee_total_max: number;
    } | null> | null;
  } | null;
};

export type SendMerchantUserInviteMutationVariables = Types.Exact<{
  merchantId: Types.Scalars['Int']['input'];
  email: Types.Scalars['String']['input'];
  permissionBits: Types.Scalars['Int']['input'];
}>;

export type SendMerchantUserInviteMutation = {
  __typename?: 'Mutation';
  send_merchant_invite: boolean;
};

export type GenerateSecretKeyMutationVariables = Types.Exact<{
  merchantId: Types.Scalars['Int']['input'];
}>;

export type GenerateSecretKeyMutation = {
  __typename?: 'Mutation';
  key: string;
};

export type UpdateCalculatorSkinMutationVariables = Types.Exact<{
  merchant_id: Types.Scalars['Int']['input'];
  main: Types.Scalars['String']['input'];
  secondary: Types.Scalars['String']['input'];
  text: Types.Scalars['String']['input'];
  period: Types.Scalars['String']['input'];
  active_period: Types.Scalars['String']['input'];
  period_text: Types.Scalars['String']['input'];
  monthly_text: Types.Scalars['String']['input'];
  button: Types.Scalars['String']['input'];
  button_text: Types.Scalars['String']['input'];
}>;

export type UpdateCalculatorSkinMutation = {
  __typename?: 'Mutation';
  skin?: {
    __typename?: 'MerchantCalculatorSkin';
    merchant_id: number;
    main: string;
    secondary: string;
    text: string;
    period: string;
    active_period: string;
    period_text: string;
    monthly_text: string;
    button: string;
    button_text: string;
  } | null;
};

export type StoreUserFromInviteByPasswordMutationVariables = Types.Exact<{
  inviteHash: Types.Scalars['String']['input'];
  email: Types.Scalars['String']['input'];
  pin: Types.Scalars['String']['input'];
  firstName: Types.Scalars['String']['input'];
  lastName: Types.Scalars['String']['input'];
  password: Types.Scalars['String']['input'];
  confirmPassword: Types.Scalars['String']['input'];
}>;

export type StoreUserFromInviteByPasswordMutation = {
  __typename?: 'Mutation';
  success: boolean;
};

export type ForwardLoanMutationVariables = Types.Exact<{
  applicationId?: Types.InputMaybe<Types.Scalars['Int']['input']>;
  creditAccountId?: Types.InputMaybe<Types.Scalars['Int']['input']>;
  merchantId?: Types.InputMaybe<Types.Scalars['Int']['input']>;
}>;

export type ForwardLoanMutation = {
  __typename?: 'Mutation';
  forward_loan: string;
};

export const CampaignBasicFragmentDoc = gql`
  fragment CampaignBasic on MerchantCampaign {
    is_active
    regular_hp_enabled
    converting_schedule_enabled
    converting_schedule_name
    converting_schedule_logo_url
    converting_schedule_net_total_min
    converting_schedule_net_total_max
    converting_schedule_months
    converting_schedule_regular_months
    converting_schedule_reverse_kickback_pct
    pay_later_enabled
    pay_later_name
    pay_later_logo_url
    pay_later_net_total_min
    pay_later_net_total_max
    pay_later_reverse_kickback_pct
    esto_pay_enabled
    regular_hp_enabled
    esto_pay_enabled
    esto_pay_name
    esto_pay_logo_url
    esto_pay_net_total_min
    esto_pay_net_total_max
    fixed_annual_pct_rate
    fixed_management_fee
    fixed_contract_fee
    converting_schedule_fixed_contract_fee
    direct_payment_gateways {
      enabled
      provider
      fee_fixed
      fee_pct
      fee_total_min
      fee_total_max
    }
  }
`;
export const CalcSkinBasicFragmentDoc = gql`
  fragment CalcSkinBasic on MerchantCalculatorSkin {
    merchant_id
    main
    secondary
    text
    period
    active_period
    period_text
    monthly_text
    button
    button_text
  }
`;
export const MerchantBasicFragmentDoc = gql`
  fragment MerchantBasic on Merchant {
    id
    shop_id
    name
    return_url
    notification_url
    cancel_url
    home_url
    logo_path
    registry_code
    phone
    address
    iban
    beneficiary_name
    cashier_loyalty_enabled
    email
    secret_key
    settings {
      merchant_financing_pct
      net_total_min
      net_total_max
      can_create_small_loan
      can_create_dynamic_loan
      can_enable_special_settings
      can_see_eligibility_status
      buyback_guarantee_days
      buyback_discount_pct
      bonus_pct
      bonus_type
      reverse_kickback_pct
      max_amount_id_verification_not_required
      min_months_period
      max_months_period
      cashier_bonus_pct
    }
    campaign {
      ...CampaignBasic
    }
    calculator_skin {
      ...CalcSkinBasic
    }
  }
  ${CampaignBasicFragmentDoc}
  ${CalcSkinBasicFragmentDoc}
`;
export const MerchantStoreBasicFragmentDoc = gql`
  fragment MerchantStoreBasic on MerchantStore {
    id
    name
    cashiers {
      id
      email
      profile {
        first_name
        last_name
      }
    }
  }
`;
export const MerchantDocument = gql`
  query Merchant($merchantId: Int!) {
    merchant(merchant_id: $merchantId) {
      ...MerchantBasic
      users {
        id
        merchant_permission_bits
        send_emails
        email
        profile {
          first_name
          last_name
        }
      }
      stores {
        ...MerchantStoreBasic
      }
    }
  }
  ${MerchantBasicFragmentDoc}
  ${MerchantStoreBasicFragmentDoc}
`;

/**
 * __useMerchantQuery__
 *
 * To run a query within a React component, call `useMerchantQuery` and pass it any options that fit your needs.
 * When your component renders, `useMerchantQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useMerchantQuery({
 *   variables: {
 *      merchantId: // value for 'merchantId'
 *   },
 * });
 */
export function useMerchantQuery(
  baseOptions: Apollo.QueryHookOptions<MerchantQuery, MerchantQueryVariables> &
    ({ variables: MerchantQueryVariables; skip?: boolean } | { skip: boolean }),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<MerchantQuery, MerchantQueryVariables>(
    MerchantDocument,
    options,
  );
}
export function useMerchantLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    MerchantQuery,
    MerchantQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<MerchantQuery, MerchantQueryVariables>(
    MerchantDocument,
    options,
  );
}
export function useMerchantSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<MerchantQuery, MerchantQueryVariables>,
) {
  const options =
    baseOptions === Apollo.skipToken
      ? baseOptions
      : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<MerchantQuery, MerchantQueryVariables>(
    MerchantDocument,
    options,
  );
}
export type MerchantQueryHookResult = ReturnType<typeof useMerchantQuery>;
export type MerchantLazyQueryHookResult = ReturnType<
  typeof useMerchantLazyQuery
>;
export type MerchantSuspenseQueryHookResult = ReturnType<
  typeof useMerchantSuspenseQuery
>;
export type MerchantQueryResult = Apollo.QueryResult<
  MerchantQuery,
  MerchantQueryVariables
>;
export const MerchantInviteDocument = gql`
  query MerchantInvite($inviteHash: String!) {
    merchant_invite(invite_hash: $inviteHash) {
      merchant_id
      hash
      permission_bits
      email
    }
  }
`;

/**
 * __useMerchantInviteQuery__
 *
 * To run a query within a React component, call `useMerchantInviteQuery` and pass it any options that fit your needs.
 * When your component renders, `useMerchantInviteQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useMerchantInviteQuery({
 *   variables: {
 *      inviteHash: // value for 'inviteHash'
 *   },
 * });
 */
export function useMerchantInviteQuery(
  baseOptions: Apollo.QueryHookOptions<
    MerchantInviteQuery,
    MerchantInviteQueryVariables
  > &
    (
      | { variables: MerchantInviteQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<MerchantInviteQuery, MerchantInviteQueryVariables>(
    MerchantInviteDocument,
    options,
  );
}
export function useMerchantInviteLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    MerchantInviteQuery,
    MerchantInviteQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<MerchantInviteQuery, MerchantInviteQueryVariables>(
    MerchantInviteDocument,
    options,
  );
}
export function useMerchantInviteSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<
        MerchantInviteQuery,
        MerchantInviteQueryVariables
      >,
) {
  const options =
    baseOptions === Apollo.skipToken
      ? baseOptions
      : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    MerchantInviteQuery,
    MerchantInviteQueryVariables
  >(MerchantInviteDocument, options);
}
export type MerchantInviteQueryHookResult = ReturnType<
  typeof useMerchantInviteQuery
>;
export type MerchantInviteLazyQueryHookResult = ReturnType<
  typeof useMerchantInviteLazyQuery
>;
export type MerchantInviteSuspenseQueryHookResult = ReturnType<
  typeof useMerchantInviteSuspenseQuery
>;
export type MerchantInviteQueryResult = Apollo.QueryResult<
  MerchantInviteQuery,
  MerchantInviteQueryVariables
>;
export const PricingDocument = gql`
  query Pricing($keys: [String]!) {
    pricing(keys: $keys) {
      key
      value
      type
    }
  }
`;

/**
 * __usePricingQuery__
 *
 * To run a query within a React component, call `usePricingQuery` and pass it any options that fit your needs.
 * When your component renders, `usePricingQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = usePricingQuery({
 *   variables: {
 *      keys: // value for 'keys'
 *   },
 * });
 */
export function usePricingQuery(
  baseOptions: Apollo.QueryHookOptions<PricingQuery, PricingQueryVariables> &
    ({ variables: PricingQueryVariables; skip?: boolean } | { skip: boolean }),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<PricingQuery, PricingQueryVariables>(
    PricingDocument,
    options,
  );
}
export function usePricingLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    PricingQuery,
    PricingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<PricingQuery, PricingQueryVariables>(
    PricingDocument,
    options,
  );
}
export function usePricingSuspenseQuery(
  baseOptions?:
    | Apollo.SkipToken
    | Apollo.SuspenseQueryHookOptions<PricingQuery, PricingQueryVariables>,
) {
  const options =
    baseOptions === Apollo.skipToken
      ? baseOptions
      : { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<PricingQuery, PricingQueryVariables>(
    PricingDocument,
    options,
  );
}
export type PricingQueryHookResult = ReturnType<typeof usePricingQuery>;
export type PricingLazyQueryHookResult = ReturnType<typeof usePricingLazyQuery>;
export type PricingSuspenseQueryHookResult = ReturnType<
  typeof usePricingSuspenseQuery
>;
export type PricingQueryResult = Apollo.QueryResult<
  PricingQuery,
  PricingQueryVariables
>;
export const UpdateMerchantDocument = gql`
  mutation UpdateMerchant(
    $merchantId: Int!
    $name: String!
    $registry_code: String!
    $address: String!
    $phone: String!
    $email: String!
    $iban: String!
    $beneficiary_name: String!
    $return_url: String
    $notification_url: String
    $cancel_url: String
    $home_url: String
  ) {
    merchant: update_merchant(
      merchant_id: $merchantId
      name: $name
      registry_code: $registry_code
      address: $address
      phone: $phone
      email: $email
      iban: $iban
      beneficiary_name: $beneficiary_name
      return_url: $return_url
      notification_url: $notification_url
      cancel_url: $cancel_url
      home_url: $home_url
    ) {
      ...MerchantBasic
    }
  }
  ${MerchantBasicFragmentDoc}
`;
export type UpdateMerchantMutationFn = Apollo.MutationFunction<
  UpdateMerchantMutation,
  UpdateMerchantMutationVariables
>;

/**
 * __useUpdateMerchantMutation__
 *
 * To run a mutation, you first call `useUpdateMerchantMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateMerchantMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateMerchantMutation, { data, loading, error }] = useUpdateMerchantMutation({
 *   variables: {
 *      merchantId: // value for 'merchantId'
 *      name: // value for 'name'
 *      registry_code: // value for 'registry_code'
 *      address: // value for 'address'
 *      phone: // value for 'phone'
 *      email: // value for 'email'
 *      iban: // value for 'iban'
 *      beneficiary_name: // value for 'beneficiary_name'
 *      return_url: // value for 'return_url'
 *      notification_url: // value for 'notification_url'
 *      cancel_url: // value for 'cancel_url'
 *      home_url: // value for 'home_url'
 *   },
 * });
 */
export function useUpdateMerchantMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdateMerchantMutation,
    UpdateMerchantMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpdateMerchantMutation,
    UpdateMerchantMutationVariables
  >(UpdateMerchantDocument, options);
}
export type UpdateMerchantMutationHookResult = ReturnType<
  typeof useUpdateMerchantMutation
>;
export type UpdateMerchantMutationResult =
  Apollo.MutationResult<UpdateMerchantMutation>;
export type UpdateMerchantMutationOptions = Apollo.BaseMutationOptions<
  UpdateMerchantMutation,
  UpdateMerchantMutationVariables
>;
export const CreateMerchantStoreDocument = gql`
  mutation CreateMerchantStore($merchantId: Int!, $name: String!) {
    store: store_merchant_store(merchant_id: $merchantId, name: $name) {
      ...MerchantStoreBasic
    }
  }
  ${MerchantStoreBasicFragmentDoc}
`;
export type CreateMerchantStoreMutationFn = Apollo.MutationFunction<
  CreateMerchantStoreMutation,
  CreateMerchantStoreMutationVariables
>;

/**
 * __useCreateMerchantStoreMutation__
 *
 * To run a mutation, you first call `useCreateMerchantStoreMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateMerchantStoreMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createMerchantStoreMutation, { data, loading, error }] = useCreateMerchantStoreMutation({
 *   variables: {
 *      merchantId: // value for 'merchantId'
 *      name: // value for 'name'
 *   },
 * });
 */
export function useCreateMerchantStoreMutation(
  baseOptions?: Apollo.MutationHookOptions<
    CreateMerchantStoreMutation,
    CreateMerchantStoreMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    CreateMerchantStoreMutation,
    CreateMerchantStoreMutationVariables
  >(CreateMerchantStoreDocument, options);
}
export type CreateMerchantStoreMutationHookResult = ReturnType<
  typeof useCreateMerchantStoreMutation
>;
export type CreateMerchantStoreMutationResult =
  Apollo.MutationResult<CreateMerchantStoreMutation>;
export type CreateMerchantStoreMutationOptions = Apollo.BaseMutationOptions<
  CreateMerchantStoreMutation,
  CreateMerchantStoreMutationVariables
>;
export const UpdateMerchantStoreDocument = gql`
  mutation UpdateMerchantStore($storeId: Int!, $name: String!) {
    store: update_merchant_store(store_id: $storeId, name: $name) {
      ...MerchantStoreBasic
    }
  }
  ${MerchantStoreBasicFragmentDoc}
`;
export type UpdateMerchantStoreMutationFn = Apollo.MutationFunction<
  UpdateMerchantStoreMutation,
  UpdateMerchantStoreMutationVariables
>;

/**
 * __useUpdateMerchantStoreMutation__
 *
 * To run a mutation, you first call `useUpdateMerchantStoreMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateMerchantStoreMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateMerchantStoreMutation, { data, loading, error }] = useUpdateMerchantStoreMutation({
 *   variables: {
 *      storeId: // value for 'storeId'
 *      name: // value for 'name'
 *   },
 * });
 */
export function useUpdateMerchantStoreMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdateMerchantStoreMutation,
    UpdateMerchantStoreMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpdateMerchantStoreMutation,
    UpdateMerchantStoreMutationVariables
  >(UpdateMerchantStoreDocument, options);
}
export type UpdateMerchantStoreMutationHookResult = ReturnType<
  typeof useUpdateMerchantStoreMutation
>;
export type UpdateMerchantStoreMutationResult =
  Apollo.MutationResult<UpdateMerchantStoreMutation>;
export type UpdateMerchantStoreMutationOptions = Apollo.BaseMutationOptions<
  UpdateMerchantStoreMutation,
  UpdateMerchantStoreMutationVariables
>;
export const UpdateMerchantUserPermissionsDocument = gql`
  mutation UpdateMerchantUserPermissions(
    $merchantId: Int!
    $userId: Int!
    $permissionBits: Int!
  ) {
    update_merchant_user_permissions(
      merchant_id: $merchantId
      user_id: $userId
      permission_bits: $permissionBits
    )
  }
`;
export type UpdateMerchantUserPermissionsMutationFn = Apollo.MutationFunction<
  UpdateMerchantUserPermissionsMutation,
  UpdateMerchantUserPermissionsMutationVariables
>;

/**
 * __useUpdateMerchantUserPermissionsMutation__
 *
 * To run a mutation, you first call `useUpdateMerchantUserPermissionsMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateMerchantUserPermissionsMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateMerchantUserPermissionsMutation, { data, loading, error }] = useUpdateMerchantUserPermissionsMutation({
 *   variables: {
 *      merchantId: // value for 'merchantId'
 *      userId: // value for 'userId'
 *      permissionBits: // value for 'permissionBits'
 *   },
 * });
 */
export function useUpdateMerchantUserPermissionsMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdateMerchantUserPermissionsMutation,
    UpdateMerchantUserPermissionsMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpdateMerchantUserPermissionsMutation,
    UpdateMerchantUserPermissionsMutationVariables
  >(UpdateMerchantUserPermissionsDocument, options);
}
export type UpdateMerchantUserPermissionsMutationHookResult = ReturnType<
  typeof useUpdateMerchantUserPermissionsMutation
>;
export type UpdateMerchantUserPermissionsMutationResult =
  Apollo.MutationResult<UpdateMerchantUserPermissionsMutation>;
export type UpdateMerchantUserPermissionsMutationOptions =
  Apollo.BaseMutationOptions<
    UpdateMerchantUserPermissionsMutation,
    UpdateMerchantUserPermissionsMutationVariables
  >;
export const DetachUserMerchantDocument = gql`
  mutation DetachUserMerchant($merchantId: Int!, $userId: Int!) {
    handle_user_merchant(
      merchant_id: $merchantId
      user_id: $userId
      attach: false
    )
  }
`;
export type DetachUserMerchantMutationFn = Apollo.MutationFunction<
  DetachUserMerchantMutation,
  DetachUserMerchantMutationVariables
>;

/**
 * __useDetachUserMerchantMutation__
 *
 * To run a mutation, you first call `useDetachUserMerchantMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDetachUserMerchantMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [detachUserMerchantMutation, { data, loading, error }] = useDetachUserMerchantMutation({
 *   variables: {
 *      merchantId: // value for 'merchantId'
 *      userId: // value for 'userId'
 *   },
 * });
 */
export function useDetachUserMerchantMutation(
  baseOptions?: Apollo.MutationHookOptions<
    DetachUserMerchantMutation,
    DetachUserMerchantMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    DetachUserMerchantMutation,
    DetachUserMerchantMutationVariables
  >(DetachUserMerchantDocument, options);
}
export type DetachUserMerchantMutationHookResult = ReturnType<
  typeof useDetachUserMerchantMutation
>;
export type DetachUserMerchantMutationResult =
  Apollo.MutationResult<DetachUserMerchantMutation>;
export type DetachUserMerchantMutationOptions = Apollo.BaseMutationOptions<
  DetachUserMerchantMutation,
  DetachUserMerchantMutationVariables
>;
export const DeleteMerchantStoreDocument = gql`
  mutation DeleteMerchantStore($storeId: Int!) {
    deleted: delete_merchant_store(store_id: $storeId)
  }
`;
export type DeleteMerchantStoreMutationFn = Apollo.MutationFunction<
  DeleteMerchantStoreMutation,
  DeleteMerchantStoreMutationVariables
>;

/**
 * __useDeleteMerchantStoreMutation__
 *
 * To run a mutation, you first call `useDeleteMerchantStoreMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteMerchantStoreMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteMerchantStoreMutation, { data, loading, error }] = useDeleteMerchantStoreMutation({
 *   variables: {
 *      storeId: // value for 'storeId'
 *   },
 * });
 */
export function useDeleteMerchantStoreMutation(
  baseOptions?: Apollo.MutationHookOptions<
    DeleteMerchantStoreMutation,
    DeleteMerchantStoreMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    DeleteMerchantStoreMutation,
    DeleteMerchantStoreMutationVariables
  >(DeleteMerchantStoreDocument, options);
}
export type DeleteMerchantStoreMutationHookResult = ReturnType<
  typeof useDeleteMerchantStoreMutation
>;
export type DeleteMerchantStoreMutationResult =
  Apollo.MutationResult<DeleteMerchantStoreMutation>;
export type DeleteMerchantStoreMutationOptions = Apollo.BaseMutationOptions<
  DeleteMerchantStoreMutation,
  DeleteMerchantStoreMutationVariables
>;
export const UpdateMerchantPaymentMethodsDocument = gql`
  mutation UpdateMerchantPaymentMethods(
    $merchantId: Int!
    $regularHpEnalbed: Boolean!
    $convertingScheduleEnabled: Boolean!
    $payLaterEnabled: Boolean!
    $estoPayEnabled: Boolean!
  ) {
    campaign: update_merchant_payment_methods(
      merchant_id: $merchantId
      regular_hp_enabled: $regularHpEnalbed
      converting_schedule_enabled: $convertingScheduleEnabled
      pay_later_enabled: $payLaterEnabled
      esto_pay_enabled: $estoPayEnabled
    ) {
      ...CampaignBasic
    }
  }
  ${CampaignBasicFragmentDoc}
`;
export type UpdateMerchantPaymentMethodsMutationFn = Apollo.MutationFunction<
  UpdateMerchantPaymentMethodsMutation,
  UpdateMerchantPaymentMethodsMutationVariables
>;

/**
 * __useUpdateMerchantPaymentMethodsMutation__
 *
 * To run a mutation, you first call `useUpdateMerchantPaymentMethodsMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateMerchantPaymentMethodsMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateMerchantPaymentMethodsMutation, { data, loading, error }] = useUpdateMerchantPaymentMethodsMutation({
 *   variables: {
 *      merchantId: // value for 'merchantId'
 *      regularHpEnalbed: // value for 'regularHpEnalbed'
 *      convertingScheduleEnabled: // value for 'convertingScheduleEnabled'
 *      payLaterEnabled: // value for 'payLaterEnabled'
 *      estoPayEnabled: // value for 'estoPayEnabled'
 *   },
 * });
 */
export function useUpdateMerchantPaymentMethodsMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdateMerchantPaymentMethodsMutation,
    UpdateMerchantPaymentMethodsMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpdateMerchantPaymentMethodsMutation,
    UpdateMerchantPaymentMethodsMutationVariables
  >(UpdateMerchantPaymentMethodsDocument, options);
}
export type UpdateMerchantPaymentMethodsMutationHookResult = ReturnType<
  typeof useUpdateMerchantPaymentMethodsMutation
>;
export type UpdateMerchantPaymentMethodsMutationResult =
  Apollo.MutationResult<UpdateMerchantPaymentMethodsMutation>;
export type UpdateMerchantPaymentMethodsMutationOptions =
  Apollo.BaseMutationOptions<
    UpdateMerchantPaymentMethodsMutation,
    UpdateMerchantPaymentMethodsMutationVariables
  >;
export const SendMerchantUserInviteDocument = gql`
  mutation SendMerchantUserInvite(
    $merchantId: Int!
    $email: String!
    $permissionBits: Int!
  ) {
    send_merchant_invite(
      merchant_id: $merchantId
      email: $email
      permission_bits: $permissionBits
    )
  }
`;
export type SendMerchantUserInviteMutationFn = Apollo.MutationFunction<
  SendMerchantUserInviteMutation,
  SendMerchantUserInviteMutationVariables
>;

/**
 * __useSendMerchantUserInviteMutation__
 *
 * To run a mutation, you first call `useSendMerchantUserInviteMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSendMerchantUserInviteMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [sendMerchantUserInviteMutation, { data, loading, error }] = useSendMerchantUserInviteMutation({
 *   variables: {
 *      merchantId: // value for 'merchantId'
 *      email: // value for 'email'
 *      permissionBits: // value for 'permissionBits'
 *   },
 * });
 */
export function useSendMerchantUserInviteMutation(
  baseOptions?: Apollo.MutationHookOptions<
    SendMerchantUserInviteMutation,
    SendMerchantUserInviteMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    SendMerchantUserInviteMutation,
    SendMerchantUserInviteMutationVariables
  >(SendMerchantUserInviteDocument, options);
}
export type SendMerchantUserInviteMutationHookResult = ReturnType<
  typeof useSendMerchantUserInviteMutation
>;
export type SendMerchantUserInviteMutationResult =
  Apollo.MutationResult<SendMerchantUserInviteMutation>;
export type SendMerchantUserInviteMutationOptions = Apollo.BaseMutationOptions<
  SendMerchantUserInviteMutation,
  SendMerchantUserInviteMutationVariables
>;
export const GenerateSecretKeyDocument = gql`
  mutation GenerateSecretKey($merchantId: Int!) {
    key: generate_secret_key(merchant_id: $merchantId)
  }
`;
export type GenerateSecretKeyMutationFn = Apollo.MutationFunction<
  GenerateSecretKeyMutation,
  GenerateSecretKeyMutationVariables
>;

/**
 * __useGenerateSecretKeyMutation__
 *
 * To run a mutation, you first call `useGenerateSecretKeyMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useGenerateSecretKeyMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [generateSecretKeyMutation, { data, loading, error }] = useGenerateSecretKeyMutation({
 *   variables: {
 *      merchantId: // value for 'merchantId'
 *   },
 * });
 */
export function useGenerateSecretKeyMutation(
  baseOptions?: Apollo.MutationHookOptions<
    GenerateSecretKeyMutation,
    GenerateSecretKeyMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    GenerateSecretKeyMutation,
    GenerateSecretKeyMutationVariables
  >(GenerateSecretKeyDocument, options);
}
export type GenerateSecretKeyMutationHookResult = ReturnType<
  typeof useGenerateSecretKeyMutation
>;
export type GenerateSecretKeyMutationResult =
  Apollo.MutationResult<GenerateSecretKeyMutation>;
export type GenerateSecretKeyMutationOptions = Apollo.BaseMutationOptions<
  GenerateSecretKeyMutation,
  GenerateSecretKeyMutationVariables
>;
export const UpdateCalculatorSkinDocument = gql`
  mutation UpdateCalculatorSkin(
    $merchant_id: Int!
    $main: String!
    $secondary: String!
    $text: String!
    $period: String!
    $active_period: String!
    $period_text: String!
    $monthly_text: String!
    $button: String!
    $button_text: String!
  ) {
    skin: update_merchant_calculator_skin(
      merchant_id: $merchant_id
      main: $main
      secondary: $secondary
      text: $text
      period: $period
      active_period: $active_period
      period_text: $period_text
      monthly_text: $monthly_text
      button: $button
      button_text: $button_text
    ) {
      ...CalcSkinBasic
    }
  }
  ${CalcSkinBasicFragmentDoc}
`;
export type UpdateCalculatorSkinMutationFn = Apollo.MutationFunction<
  UpdateCalculatorSkinMutation,
  UpdateCalculatorSkinMutationVariables
>;

/**
 * __useUpdateCalculatorSkinMutation__
 *
 * To run a mutation, you first call `useUpdateCalculatorSkinMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateCalculatorSkinMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateCalculatorSkinMutation, { data, loading, error }] = useUpdateCalculatorSkinMutation({
 *   variables: {
 *      merchant_id: // value for 'merchant_id'
 *      main: // value for 'main'
 *      secondary: // value for 'secondary'
 *      text: // value for 'text'
 *      period: // value for 'period'
 *      active_period: // value for 'active_period'
 *      period_text: // value for 'period_text'
 *      monthly_text: // value for 'monthly_text'
 *      button: // value for 'button'
 *      button_text: // value for 'button_text'
 *   },
 * });
 */
export function useUpdateCalculatorSkinMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdateCalculatorSkinMutation,
    UpdateCalculatorSkinMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpdateCalculatorSkinMutation,
    UpdateCalculatorSkinMutationVariables
  >(UpdateCalculatorSkinDocument, options);
}
export type UpdateCalculatorSkinMutationHookResult = ReturnType<
  typeof useUpdateCalculatorSkinMutation
>;
export type UpdateCalculatorSkinMutationResult =
  Apollo.MutationResult<UpdateCalculatorSkinMutation>;
export type UpdateCalculatorSkinMutationOptions = Apollo.BaseMutationOptions<
  UpdateCalculatorSkinMutation,
  UpdateCalculatorSkinMutationVariables
>;
export const StoreUserFromInviteByPasswordDocument = gql`
  mutation StoreUserFromInviteByPassword(
    $inviteHash: String!
    $email: String!
    $pin: String!
    $firstName: String!
    $lastName: String!
    $password: String!
    $confirmPassword: String!
  ) {
    success: store_user_from_merchant_invite(
      invite_hash: $inviteHash
      email: $email
      pin: $pin
      first_name: $firstName
      last_name: $lastName
      password: $password
      confirm_password: $confirmPassword
    )
  }
`;
export type StoreUserFromInviteByPasswordMutationFn = Apollo.MutationFunction<
  StoreUserFromInviteByPasswordMutation,
  StoreUserFromInviteByPasswordMutationVariables
>;

/**
 * __useStoreUserFromInviteByPasswordMutation__
 *
 * To run a mutation, you first call `useStoreUserFromInviteByPasswordMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useStoreUserFromInviteByPasswordMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [storeUserFromInviteByPasswordMutation, { data, loading, error }] = useStoreUserFromInviteByPasswordMutation({
 *   variables: {
 *      inviteHash: // value for 'inviteHash'
 *      email: // value for 'email'
 *      pin: // value for 'pin'
 *      firstName: // value for 'firstName'
 *      lastName: // value for 'lastName'
 *      password: // value for 'password'
 *      confirmPassword: // value for 'confirmPassword'
 *   },
 * });
 */
export function useStoreUserFromInviteByPasswordMutation(
  baseOptions?: Apollo.MutationHookOptions<
    StoreUserFromInviteByPasswordMutation,
    StoreUserFromInviteByPasswordMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    StoreUserFromInviteByPasswordMutation,
    StoreUserFromInviteByPasswordMutationVariables
  >(StoreUserFromInviteByPasswordDocument, options);
}
export type StoreUserFromInviteByPasswordMutationHookResult = ReturnType<
  typeof useStoreUserFromInviteByPasswordMutation
>;
export type StoreUserFromInviteByPasswordMutationResult =
  Apollo.MutationResult<StoreUserFromInviteByPasswordMutation>;
export type StoreUserFromInviteByPasswordMutationOptions =
  Apollo.BaseMutationOptions<
    StoreUserFromInviteByPasswordMutation,
    StoreUserFromInviteByPasswordMutationVariables
  >;
export const ForwardLoanDocument = gql`
  mutation ForwardLoan(
    $applicationId: Int
    $creditAccountId: Int
    $merchantId: Int
  ) {
    forward_loan(
      application_id: $applicationId
      credit_account_id: $creditAccountId
      merchant_id: $merchantId
    )
  }
`;
export type ForwardLoanMutationFn = Apollo.MutationFunction<
  ForwardLoanMutation,
  ForwardLoanMutationVariables
>;

/**
 * __useForwardLoanMutation__
 *
 * To run a mutation, you first call `useForwardLoanMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useForwardLoanMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [forwardLoanMutation, { data, loading, error }] = useForwardLoanMutation({
 *   variables: {
 *      applicationId: // value for 'applicationId'
 *      creditAccountId: // value for 'creditAccountId'
 *      merchantId: // value for 'merchantId'
 *   },
 * });
 */
export function useForwardLoanMutation(
  baseOptions?: Apollo.MutationHookOptions<
    ForwardLoanMutation,
    ForwardLoanMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<ForwardLoanMutation, ForwardLoanMutationVariables>(
    ForwardLoanDocument,
    options,
  );
}
export type ForwardLoanMutationHookResult = ReturnType<
  typeof useForwardLoanMutation
>;
export type ForwardLoanMutationResult =
  Apollo.MutationResult<ForwardLoanMutation>;
export type ForwardLoanMutationOptions = Apollo.BaseMutationOptions<
  ForwardLoanMutation,
  ForwardLoanMutationVariables
>;
