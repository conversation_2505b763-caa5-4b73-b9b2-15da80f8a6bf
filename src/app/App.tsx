import 'shared/models/analytics';

import { withProviders } from 'app/providers';
import { GlobalStateProvider } from 'modules/GlobalStateProvider';
import router from 'pages';
import { Suspense, useEffect } from 'react';
import { RouterProvider } from 'react-router-dom';
import { Loader } from 'shared/components';
import { useGoogleAnalytics } from 'shared/hooks/analytics';
import { appModel } from 'shared/models/app';
import { googleGtag } from 'shared/services/google-gtag';

const AnalyticksWrapper = ({ children }: { children: React.ReactNode }) => {
  useGoogleAnalytics();

  return <>{children}</>;
};

const App = () => {
  useEffect(() => {
    googleGtag.init();
    appModel.initAppEv();
  }, []);

  return (
    <GlobalStateProvider>
      <Suspense fallback={<Loader fullScreen />}>
        <AnalyticksWrapper>
          <RouterProvider router={router} />
        </AnalyticksWrapper>
      </Suspense>
    </GlobalStateProvider>
  );
};

export default withProviders(App);
