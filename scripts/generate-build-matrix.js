/* eslint-disable no-undef */
const MATRIX_MAP = {
  'refs/heads/master': [
    // {
    // 	dist: 'prod-et',
    // 	certificateArn:
    // 		'arn:aws:acm:us-east-1:676984888906:certificate/0ab83952-d7e7-4bfb-b89b-980b98b355c6',
    // 	domain: 'affiliate.esto.ee',
    // 	cloudflareZoneId: '3849ef2174b7824e743eeb1f20e64a5e',
    // },
    {
      dist: 'prod-lt',
      certificateArn:
        'arn:aws:acm:us-east-1:676984888906:certificate/8e4b69d7-b80a-4681-ae23-320bf83565e6',
      domain: 'affiliate.estopay.lt',
      cloudflareZoneId: 'fb2feef762b37a4efe01406211adbc35',
    },
    // {
    // 	dist: 'prod-lv',
    // 	certificateArn:
    // 		'arn:aws:acm:us-east-1:676984888906:certificate/993e0cae-58a9-49b0-a79f-c407b7c1a2f6',
    // 	domain: 'affiliate.esto.lv',
    // 	cloudflareZoneId: 'e4c890c5b151ea7329b96761c8384d5e',
    // },
  ],
  // 'refs/heads/develop': [
  // 	{
  // 		dist: 'dekker',
  // 		certificateArn:
  // 			'arn:aws:acm:us-east-1:676984888906:certificate/7f69df02-f7eb-45d0-b8ec-8ba4767d8fa1',
  // 		domain: 'affiliate.dekker.ee',
  // 		cloudflareZoneId: 'dc9975e152873973a4159f0a8a6842d8',
  // 	},
  // 	{
  // 		dist: 'dekker-lt',
  // 		certificateArn:
  // 			'arn:aws:acm:us-east-1:676984888906:certificate/b7266782-3845-4027-9a17-d1aa54425791',
  // 		domain: 'affiliate.dekker.lt',
  // 		cloudflareZoneId: '4f62360e97e0f6da6615095d3b75ecbc',
  // 	},
  // 	{
  // 		dist: 'dekker-lv',
  // 		certificateArn:
  // 			'arn:aws:acm:us-east-1:676984888906:certificate/e4e2a5e6-d610-4160-b55a-14250fa8754a',
  // 		domain: 'affiliate.dekker.lv',
  // 		cloudflareZoneId: 'f89c81a18c404b24f2f5aa635c320eb8',
  // 	},
  // ],
  // 'refs/heads/dekker-dev0': [
  // 	{
  // 		dist: 'dekker-dev0',
  // 		certificateArn:
  // 			'arn:aws:acm:us-east-1:676984888906:certificate/9187ba2a-3750-4bba-a87c-e0c63e7805a0',
  // 		domain: 'affiliate.dekker-dev0.ee',
  // 		cloudflareZoneId: 'd65d3aa32de0a0e74823ad9041ed547c',
  // 	},
  // ],
  // 'refs/heads/dekker-dev1': [
  // 	{
  // 		dist: 'dekker-dev1',
  // 		certificateArn:
  // 			'arn:aws:acm:us-east-1:676984888906:certificate/5a19e912-ffe2-4915-9ad2-4ebc8fcbeaf3',
  // 		domain: 'affiliate.dekker-dev1.ee',
  // 		cloudflareZoneId: 'a1315167964e9fe154ac634205dd855e',
  // 	},
  // ],
  // 'refs/heads/dekker-dev2': [
  // 	{
  // 		dist: 'dekker-dev2',
  // 		certificateArn:
  // 			'arn:aws:acm:us-east-1:676984888906:certificate/d09557d4-0eb8-4143-8e11-864b163cb8e3',
  // 		domain: 'affiliate.dekker-dev2.ee',
  // 		cloudflareZoneId: 'cd77d6facc109467e5da26a569a9b060',
  // 	},
  // ],
};

const gitRef = process.argv[2];

let matrix = {};

if (!(gitRef in MATRIX_MAP)) {
  matrix = {};
} else {
  matrix = { include: MATRIX_MAP[gitRef] };
}

console.log('Build matrix is:');
console.log(JSON.stringify(matrix, null, 2));

console.log(`::set-output name=matrix::${JSON.stringify(matrix)}`);
