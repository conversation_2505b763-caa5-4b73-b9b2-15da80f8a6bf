# Esto Partner v2

## Docker Setup

This project is containerized using Docker and can be run using either Docker Compose directly or through the provided Makefile commands.

### Prerequisites

- Docker
- Docker Compose
- Make (optional, for using Makefile commands)

### Using Makefile (Recommended)

The project includes a Makefile with common Docker commands:

```bash
# Build the Docker image
make build

# Start the containers
make up

# Start the containers in detached mode
make up-d

# Stop the containers
make down

# View container logs
make logs

# Clean up containers, images, and volumes
make clean

# Rebuild and start containers
make rebuild

# Rebuild and start containers in detached mode
make rebuild-d
```

### Using Docker Compose Directly

If you prefer not to use the Makefile, you can use Docker Compose commands directly:

```bash
# Build the image
docker-compose build

# Start the containers
docker-compose up

# Start in detached mode
docker-compose up -d

# Stop the containers
docker-compose down

# View logs
docker-compose logs -f
```

### Accessing the Application

Once the containers are running, the application will be available at:

- http://localhost:3020

### Development

The Docker setup includes:

- Hot reloading for development
- Volume mounts for local development
- pnpm for package management
- Node.js 18 Alpine as the base image

### Environment Variables

The application uses environment variables from the `.env` files. Make sure to set up the appropriate environment file before running the application.

## Additional Documentation

For more information about the project setup and development, please refer to the project documentation.

## Development

To run the development server:

```bash
npm start
```

## Hint for local(dekker) development

**(Optional) If you use MacOS with M1 processor, you should to use Node v.15. How to install and use different versions of Node, you can see here:**

[https://tecadmin.net/install-nvm-macos-with-homebrew/].
When nvm installed, run commands `nvm install 15` and then `nvm use 15`. You should see an alert like this 'Now using node v15.14.0 (npm v7.7.6)'

**How set up local environment for using one of the dev<number> api server locally.**

If you need locally use one of dev(for example dev1) api server because `meQuery` returns `null` see the folowing instruction for the `https://api.dekker-dev1.ee/` for example:

- check .env file that it contains the settings with required version 'dekker-dev1'. Creating by analogy with existing versions, if not.
- add localhost host `affiliate.dekker-dev1.ee` to your hosts file. For the Linux(Ubuntu) you can find this file in `/etc/hosts` and you should add the new host for `127.0.0.1`. Run command on terminal `sudo nano /etc/hosts` and for example you should add something like "127.0.0.1 localhost, affiliate.esto.test, affiliate.dekker-dev0.ee, affiliate.dekker-dev1.ee, ...". Then save changes with keyboard shortcuts.
- Delete `node_modules` folder (if you have one), then run `npm install`.
- Use the command from `package.json`: `serve:domain-dev1`. To launch on localhost open `https://affiliate.dekker-dev1.ee:3000`.

## Local domain

- Run `npm run certificate:mac` to install ssl certificate (on macOS) if you want to run with local domain `https://affiliate.esto.test:3000/`. It will prompt your admin password.
- After certificate installed run `npm run serve:domain`

**If you are doing something with GraphQL, see below for details.**

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Project Structure

All components should be added inside `src/components` folder.\
If new component will be reused across multiple pages it should be placed at `src/components/shared`.\
If new component will be used only on one page it should be placed at `src/components/{pageName}`.\
There should be no top level exports from `src/components`. You can import component like this:

```bash
import { SomeSharedComponent } from 'src/modules/shared';
```

or

```bash
import { SomePageSpecificComponent } from 'src/modules/{pageName}';
```

## GraphQL

We are using apollo codegen to work with graphql.

To update schema run:

```bash
npm run graphql-update-schema:dekker
```

You can use other environments than `dekker`

In watch mode codegen will take every `gql` tag from `ts(x)` files, and generate typings using downloaded schema.

To start codegen in watch mode run:

```bash
npm run graphql-codegen:watch
```

## I18n

I18n is implemented using `react-i18next` library. To manage translations we are using [locize](https://locize.app).
For additional info about this service please refer to official documentation.
The usual flow is to add translation keys and EN locale translations from designs/copywriters to corresponding namespaces during development.
The project uses the useTranslation hook into which you need to pass the namespace. The hook returns a function to be called in place of the text and passed the translation key value into it.
Example:
`const { t } = useTranslation('terminal');`
`<p>{t(select-store.title)}</p>`

If there's a need to import a file with translations:

First, export file from locize with all translations in desired format to see how it should be composed. Then you can add yours translations or create and import another file in the same format as imported one.
For more info read - https://docs.locize.com/more/general-questions/how-to-import-translations-from-a-file

There are two versions of translations: latest and production.
First, we add them to 'latest' and when you are sure the translations are right and approved - copy them from 'latest' to 'production' (make it by opening options in 'Versions' block).

## Deployment

**Any change to infrastructure should go there, and be applied automatically from CI**

Deployment is set via AWS Amplify

https://estoas.atlassian.net/wiki/spaces/EC/pages/3811803140/AWS+Amplify+CI+CD+Setup+Guide

1. Pushes to master will trigger deployment on EE,LV,LT production.
2. Pushes to dekker branches will trigger deployment to stage.
3. All setup is on AWS Amplify Console side.
4. Ask for permission to make changes there.
